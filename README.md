# LuminariMUD Source Code

A MUD (Multi-User Dungeon) based on Pathfinder/d20/D&D 3.5 mechanics, built on TBA/CWG/d20 codebases.

## Quick Links
- **Documentation**: [Original tbaMUD/CircleMUD docs](https://github.com/LuminariMUD/LuminariMUD/tree/master/doc)
- **Discord**: [Join our community](https://discord.gg/Me3Tuu4)

## Vision
Create a MUD with Pathfinder/d20/D&D 3.5 mechanics featuring an original world inspired by Biblical, Dragonlance, and Forgotten Realms stories. Our primary goal is building a safe, friendly community for like-minded gamers.

## Project Philosophy
This project requires commitment, self-motivation, and the ability to work through challenges. Creating a MUD is rewarding work in itself, regardless of player base size. We're committed to the initial vision and hard work to make this project successful.

## Staff Structure

### Website Team
- **Blog Manager**: Create and manage weekly code update posts
- **Forum Manager**: Maintain community discussions and content organization

### Content Team
- **Help File Lead**: Organize help system and keyword content
- **Help File Editors**: Create comprehensive help documentation
- **World Designer**: Design maps, zones, and building standards
- **Lore Designer**: Develop world background, stories, and inhabitant relationships
- **Quest Designers**: Create consistent quest rewards and lore integration
- **Builders**: Create world content, scripts, and quests
- **Lead Scripter**: Develop universal scripts and provide builder support

### Development Team
- **Lead Programmer**: Manage code standards, SVN, and development workflow
- **Game Designer**: Define game mechanics and project direction
- **Programmers**: Implement game mechanics and features

### Administration Team
- **Lead Administrator**: Manage staff and enforce conduct rules
- **Administrators**: Support player relations and enforce community standards
- **Recruiter**: Community outreach and new staff onboarding

## World Design Goals
- **World Map Navigation**: Zone-to-zone travel system with vehicle support
- **Quest-Driven Progression**: Story-oriented advancement system
- **Living World**: Heavy scripting for dynamic, responsive environments
- **High-Quality Content**: Replace stock zones with original, redesigned content
- **Pathfinder/D&D 3.5 Mechanics**: Familiar rule system implementation

## Staff Guidelines

### Core Principles
- **Commitment**: Dedication to the project vision and community
- **Leadership by Example**: Lead through actions, not just words
- **Respect Boundaries**: Stay within your area of expertise
- **Time Investment**: Value others' contributions and time equally
- **Community First**: Prioritize community needs over individual preferences

### Activity Requirements
- **All Staff**: 6 weeks of inactivity without contact may result in demotion
- **Leaders**: Must be familiar with all base races/classes and MUD lore
- **Builders**: Minimum 3 "things" per week (see Lead Builder for details)
- **Administrators**: Required active time on MUD (see Lead Admin)

### Conflict of Interest
Leadership positions require primary commitment to LuminariMUD. Staff cannot hold equal or greater leadership roles in competing MUDs.

## Code Access
- Anyone may request the source code
- Builders may request copies of their zones (see in-game DISCLAIMER for details)

## Final Authority
The project lead reserves the right to make final decisions on matters affecting the initial vision or based on MUDding experience.

---

*Remember: The work itself is the reward. Focus on creating something meaningful for the community.*
